-- Steal A Fish WindUI Script
-- Load WindUI Library
local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()

-- Services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

-- Variables
local LocalPlayer = Players.LocalPlayer
local Character = LocalPlayer.Character or LocalPlayer.CharacterAdded:Wait()
local Humanoid = Character:WaitForChild("Humanoid")
local RootPart = Character:WaitForChild("HumanoidRootPart")

-- Player Variables
local originalWalkSpeed = Humanoid.WalkSpeed
local originalJumpPower = Humanoid.JumpPower
local noclipEnabled = false
local infJumpEnabled = false
local noclipConnection

-- Auto Lock Variables
local autoLockEnabled = false
local autoLockConnection
local playerTycoon = nil

-- Create Window
local Window = WindUI:CreateWindow({
    Title = "Steal A Fish Hub",
    Icon = "fish",
    Author = "WindUI Script",
    Folder = "StealAFishHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    Resizable = true,
    SideBarWidth = 200,
})

-- Create Tabs
local MainTab = Window:Tab({
    Title = "Main",
    Icon = "home",
})

local PlayerTab = Window:Tab({
    Title = "Player",
    Icon = "user",
})

local TeleportTab = Window:Tab({
    Title = "Teleport",
    Icon = "map-pin",
})

local ESPTab = Window:Tab({
    Title = "ESP",
    Icon = "eye",
})

local SettingsTab = Window:Tab({
    Title = "Settings",
    Icon = "settings",
})

-- MAIN TAB (Placeholders)
MainTab:Toggle({
    Title = "Auto Lock",
    Desc = "Automatically lock onto targets",
    Icon = "target",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        autoLockEnabled = state
        
        if autoLockEnabled then
            autoLockConnection = RunService.Heartbeat:Connect(function()
                if not workspace:FindFirstChild("Map") or not workspace.Map:FindFirstChild("Tycoons") then
                    return
                end
                
                for i = 1, 8 do
                    local tycoon = workspace.Map.Tycoons:FindFirstChild("Tycoon" .. i)
                    if tycoon and tycoon:FindFirstChild("Board") then
                        local boardPart = nil
                        for _, child in pairs(tycoon.Board:GetChildren()) do
                            if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                boardPart = child
                                break
                            end
                        end
                        
                        if boardPart then
                            local usernameLabel = boardPart.SurfaceGui.Username
                            if usernameLabel.Text == LocalPlayer.Name then
                                if tycoon:FindFirstChild("ForcefieldFolder") and 
                                   tycoon.ForcefieldFolder:FindFirstChild("Screen") and 
                                   tycoon.ForcefieldFolder.Screen:FindFirstChild("Screen") and
                                   tycoon.ForcefieldFolder.Screen.Screen:FindFirstChild("SurfaceGui") and
                                   tycoon.ForcefieldFolder.Screen.Screen.SurfaceGui:FindFirstChild("Time") then
                                    
                                    local timeLabel = tycoon.ForcefieldFolder.Screen.Screen.SurfaceGui.Time
                                    if timeLabel.Text == "0s" then
                                        if tycoon.ForcefieldFolder:FindFirstChild("Buttons") and 
                                           tycoon.ForcefieldFolder.Buttons:FindFirstChild("ForceFieldBuy") then
                                            
                                            local forceFieldBuy = tycoon.ForcefieldFolder.Buttons.ForceFieldBuy
                                            if RootPart then
                                                RootPart.CFrame = forceFieldBuy.CFrame
                                            end
                                        end
                                    end
                                end
                                break
                            end
                        end
                    end
                end
            end)
        else
            if autoLockConnection then
                autoLockConnection:Disconnect()
                autoLockConnection = nil
            end
            playerTycoon = nil
        end
    end
})

MainTab:Toggle({
    Title = "Instant Proximity",
    Desc = "Instant proximity detection",
    Icon = "zap",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Instant Proximity:", state)
        -- TODO: Implement Instant Proximity functionality
    end
})

MainTab:Toggle({
    Title = "Full Bright",
    Desc = "Remove darkness from the game",
    Icon = "sun",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Full Bright:", state)
        -- TODO: Implement Full Bright functionality
    end
})

MainTab:Button({
    Title = "Debug Info",
    Desc = "Show player tycoon and countdown info",
    Icon = "info",
    Callback = function()
        local debugInfo = "=== AUTO LOCK DEBUG INFO ===\n"
        debugInfo = debugInfo .. "Player Name: " .. LocalPlayer.Name .. "\n"
        debugInfo = debugInfo .. "Looking for tycoons...\n"
        
        -- Check if workspace.Map.Tycoons exists
        if not workspace:FindFirstChild("Map") then
            debugInfo = debugInfo .. "ERROR: workspace.Map not found\n"
        elseif not workspace.Map:FindFirstChild("Tycoons") then
            debugInfo = debugInfo .. "ERROR: workspace.Map.Tycoons not found\n"
        else
            debugInfo = debugInfo .. "Found workspace.Map.Tycoons\n"
            
            -- List all tycoons
            for i = 1, 8 do
                local tycoon = workspace.Map.Tycoons:FindFirstChild("Tycoon" .. i)
                if tycoon then
                    debugInfo = debugInfo .. "Found " .. tycoon.Name .. "\n"
                    
                    -- Check board structure
                    if tycoon:FindFirstChild("Board") then
                        debugInfo = debugInfo .. "  - Has Board (Group)\n"
                        local boardPart = nil
                        for _, child in pairs(tycoon.Board:GetChildren()) do
                            debugInfo = debugInfo .. "    - Child: " .. child.Name .. " (" .. child.ClassName .. ")\n"
                            if child:IsA("BasePart") and child:FindFirstChild("SurfaceGui") and child.SurfaceGui:FindFirstChild("Username") then
                                boardPart = child
                                break
                            end
                        end
                        
                        if boardPart then
                            debugInfo = debugInfo .. "  - Found board part: " .. boardPart.Name .. "\n"
                            local username = boardPart.SurfaceGui.Username.Text
                            debugInfo = debugInfo .. "  - Username: " .. username .. "\n"
                            if username == LocalPlayer.Name then
                                debugInfo = debugInfo .. "  - MATCH! This is our tycoon!\n"
                                playerTycoon = tycoon
                            end
                        else
                            debugInfo = debugInfo .. "  - No board part with SurfaceGui found\n"
                        end
                    else
                        debugInfo = debugInfo .. "  - No Board\n"
                    end
                else
                    debugInfo = debugInfo .. "Tycoon" .. i .. " not found\n"
                end
            end
        end
        
        if playerTycoon then
            debugInfo = debugInfo .. "Player Tycoon: " .. playerTycoon.Name .. "\n"
            
            -- Check forcefield structure
            if playerTycoon:FindFirstChild("ForcefieldFolder") then
                debugInfo = debugInfo .. "  - Has ForcefieldFolder\n"
                if playerTycoon.ForcefieldFolder:FindFirstChild("Screen") then
                    debugInfo = debugInfo .. "  - Has Screen\n"
                    if playerTycoon.ForcefieldFolder.Screen:FindFirstChild("Screen") then
                        debugInfo = debugInfo .. "  - Has Screen.Screen\n"
                        if playerTycoon.ForcefieldFolder.Screen.Screen:FindFirstChild("SurfaceGui") then
                            debugInfo = debugInfo .. "  - Has SurfaceGui\n"
                            if playerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui:FindFirstChild("Time") then
                                local timeLabel = playerTycoon.ForcefieldFolder.Screen.Screen.SurfaceGui.Time
                                debugInfo = debugInfo .. "Countdown: " .. timeLabel.Text .. "\n"
                            else
                                debugInfo = debugInfo .. "  - No Time label\n"
                            end
                        else
                            debugInfo = debugInfo .. "  - No SurfaceGui in Screen\n"
                        end
                    else
                        debugInfo = debugInfo .. "  - No Screen.Screen\n"
                    end
                else
                    debugInfo = debugInfo .. "  - No Screen\n"
                end
            else
                debugInfo = debugInfo .. "  - No ForcefieldFolder\n"
            end
        else
            debugInfo = debugInfo .. "Player Tycoon: Not found\n"
            debugInfo = debugInfo .. "Countdown: N/A\n"
        end
        
        debugInfo = debugInfo .. "Auto Lock Status: " .. (autoLockEnabled and "Enabled" or "Disabled") .. "\n"
        debugInfo = debugInfo .. "=========================="
        
        print(debugInfo)
    end
})

-- PLAYER TAB (Fully Implemented)
-- Speed Modifier
PlayerTab:Slider({
    Title = "Speed Modifier",
    Desc = "Adjust your walking speed",
    Step = 1,
    Value = {
        Min = 1,
        Max = 100,
        Default = 16,
    },
    Callback = function(value)
        if Character and Character:FindFirstChild("Humanoid") then
            Character.Humanoid.WalkSpeed = value
        end
    end
})

-- Jump Modifier
PlayerTab:Slider({
    Title = "Jump Modifier",
    Desc = "Adjust your jump power",
    Step = 1,
    Value = {
        Min = 1,
        Max = 200,
        Default = 50,
    },
    Callback = function(value)
        if Character and Character:FindFirstChild("Humanoid") then
            Character.Humanoid.JumpPower = value
        end
    end
})

-- Noclip Toggle
PlayerTab:Toggle({
    Title = "Noclip",
    Desc = "Walk through walls and objects",
    Icon = "ghost",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        noclipEnabled = state

        if noclipEnabled then
            noclipConnection = RunService.Stepped:Connect(function()
                if Character then
                    for _, part in pairs(Character:GetChildren()) do
                        if part:IsA("BasePart") and part.CanCollide then
                            part.CanCollide = false
                        end
                    end
                end
            end)
        else
            if noclipConnection then
                noclipConnection:Disconnect()
                noclipConnection = nil
            end

            if Character then
                for _, part in pairs(Character:GetChildren()) do
                    if part:IsA("BasePart") and part.Name ~= "HumanoidRootPart" then
                        part.CanCollide = true
                    end
                end
            end
        end
    end
})

-- Infinite Jump Toggle
PlayerTab:Toggle({
    Title = "Infinite Jump",
    Desc = "Jump infinitely in the air",
    Icon = "arrow-up",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        infJumpEnabled = state
    end
})

-- Infinite Jump Implementation
UserInputService.JumpRequest:Connect(function()
    if infJumpEnabled and Character and Character:FindFirstChild("Humanoid") then
        Character.Humanoid:ChangeState(Enum.HumanoidStateType.Jumping)
    end
end)

-- Character Respawn Handler
LocalPlayer.CharacterAdded:Connect(function(newCharacter)
    Character = newCharacter
    Humanoid = Character:WaitForChild("Humanoid")
    RootPart = Character:WaitForChild("HumanoidRootPart")

    -- Reset values
    originalWalkSpeed = Humanoid.WalkSpeed
    originalJumpPower = Humanoid.JumpPower

    -- Reset auto lock variables
    playerTycoon = nil

    -- Reapply noclip if enabled
    if noclipEnabled and noclipConnection then
        noclipConnection:Disconnect()
        noclipConnection = RunService.Stepped:Connect(function()
            if Character then
                for _, part in pairs(Character:GetChildren()) do
                    if part:IsA("BasePart") and part.CanCollide then
                        part.CanCollide = false
                    end
                end
            end
        end)
    end
end)

-- TELEPORT TAB (Placeholder)
TeleportTab:Toggle({
    Title = "Teleport System",
    Desc = "Teleportation features coming soon",
    Icon = "map-pin",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Teleport System:", state)
        -- TODO: Implement Teleport functionality
    end
})

-- ESP TAB (Placeholder)
ESPTab:Toggle({
    Title = "Player ESP",
    Desc = "ESP features coming soon",
    Icon = "eye",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Player ESP:", state)
        -- TODO: Implement ESP functionality
    end
})

-- SETTINGS TAB (Placeholder)
SettingsTab:Toggle({
    Title = "Save Configuration",
    Desc = "Settings features coming soon",
    Icon = "save",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        print("Save Configuration:", state)
        -- TODO: Implement Settings functionality
    end
})

print("Steal A Fish WindUI Script Loaded Successfully!")